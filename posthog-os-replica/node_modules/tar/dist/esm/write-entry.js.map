{"version": 3, "file": "write-entry.js", "sourceRoot": "", "sources": ["../../src/write-entry.ts"], "names": [], "mappings": "AAAA,OAAO,EAAkB,MAAM,IAAI,CAAA;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAA;AACnC,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AACpC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAA;AACvC,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAClE,OAAO,EACL,OAAO,GAIR,MAAM,cAAc,CAAA;AACrB,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAA;AAE9B,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAA;AAC5D,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAElE,OAAO,EAIL,UAAU,GACX,MAAM,kBAAkB,CAAA;AACzB,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAA;AAEzC,MAAM,UAAU,GAAG,CAAC,IAAY,EAAE,MAAe,EAAE,EAAE;IACnD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAA;IACnC,CAAC;IACD,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;IAC1D,OAAO,oBAAoB,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAA;AAClD,CAAC,CAAA;AAED,MAAM,WAAW,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAA;AAEpC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAE/B,MAAM,OAAO,UACX,SAAQ,QAAoD;IAG5D,IAAI,CAAQ;IACZ,QAAQ,CAAS;IACjB,KAAK,GAAW,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAA;IACzD,6DAA6D;IAC7D,MAAM,GAAW,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAA;IACvC,WAAW,CAAQ;IACnB,SAAS,CAA6C;IACtD,SAAS,CAA6C;IACtD,aAAa,CAAS;IACtB,GAAG,CAAQ;IACX,MAAM,CAAS;IACf,KAAK,CAAO;IACZ,KAAK,CAAS;IACd,OAAO,CAAS;IAChB,MAAM,CAAS;IACf,EAAE,CAAS;IAEX,QAAQ,GAAW,CAAC,CAAA;IACpB,WAAW,GAAW,CAAC,CAAA;IACvB,GAAG,CAAS;IACZ,GAAG,GAAW,CAAC,CAAA;IACf,MAAM,GAAW,CAAC,CAAA;IAClB,MAAM,GAAW,CAAC,CAAA;IAClB,MAAM,GAAW,CAAC,CAAA;IAElB,KAAK,CAAS;IACd,QAAQ,CAAQ;IAEhB,MAAM,CAAS;IACf,IAAI,CAAgC;IACpC,QAAQ,CAAS;IACjB,IAAI,CAAQ;IACZ,YAAY,CAA6B;IAEzC,SAAS,GAAY,KAAK,CAAA;IAE1B,YAAY,CAAS,EAAE,OAA8B,EAAE;QACrD,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACzB,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAA;QACnC,gDAAgD;QAChD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAA;QAC9B,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,IAAI,WAAW,CAAA;QACjD,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAI,GAAG,EAAE,CAAA;QAC3C,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAI,GAAG,EAAE,CAAA;QAC3C,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAA;QACxC,IAAI,CAAC,GAAG,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;QACzD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAA;QAC5B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;QACtB,IAAI,CAAC,MAAM;YACT,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAC3D,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAA;QAEpC,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACrC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,QAAQ,GAAqB,KAAK,CAAA;QACtC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACrD,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACzC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;gBACpB,QAAQ,GAAG,IAAI,CAAA;YACjB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAA;QACxD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,gEAAgE;YAChE,+DAA+D;YAC/D,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAA;YAC1D,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC3B,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAClC,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAC1C,CAAA;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CACP,gBAAgB,EAChB,aAAa,QAAQ,qBAAqB,EAC1C;gBACE,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI;aAC3B,CACF,CAAA;QACH,CAAC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC5C,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAA;QACnB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACf,CAAC;IACH,CAAC;IAED,IAAI,CAAC,IAAY,EAAE,OAAuB,EAAE,OAAiB,EAAE;QAC7D,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED,IAAI,CAAC,EAAmB,EAAE,GAAG,IAAW;QACtC,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QACvB,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;IAChC,CAAC;IAED,CAAC,KAAK,CAAC;QACL,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACnC,IAAI,EAAE,EAAE,CAAC;gBACP,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YAC/B,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAA;QACrB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,IAAW;QACnB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;QACf,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;IACjB,CAAC;IAED,CAAC,OAAO,CAAC;QACP,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;YACrB,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAA;YAC1B,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;YACxB,iCAAiC;YACjC;gBACE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAA;QACrB,CAAC;IACH,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,IAAY;QACjB,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;IAChE,CAAC;IAED,CAAC,MAAM,CAAC,CAAC,IAAY;QACnB,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IAED,CAAC,MAAM,CAAC;QACN,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;QACpD,CAAC;QACD,oBAAoB;QAEpB,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACrB,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,CAAA;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;YACvB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAC7B,uCAAuC;YACvC,QAAQ,EACN,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC7B,CAAC,CAAC,IAAI,CAAC,QAAQ;YACjB,yDAAyD;YACzD,mDAAmD;YACnD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAChC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC9C,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC9C,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YACpB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK;YAC/D,oBAAoB;YACpB,IAAI,EAAE,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;YACzD,KAAK,EACH,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;gBACzB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;oBAC5C,CAAC,CAAC,EAAE;YACN,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;YAClD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;SACnD,CAAC,CAAA;QAEF,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACxC,KAAK,CAAC,KAAK,CACT,IAAI,GAAG,CAAC;gBACN,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK;gBACpD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK;gBACpD,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG;gBAChD,KAAK,EACH,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CACzB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAChC;gBACH,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC7B,QAAQ,EACN,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;oBACnD,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC7B,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACjB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;gBACtB,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG;gBAChD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK;gBACpD,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC9C,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC9C,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;aACnD,CAAC,CAAC,MAAM,EAAE,CACZ,CAAA;QACH,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAA;QAChC,qBAAqB;QACrB,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;QAC5C,CAAC;QACD,oBAAoB;QACpB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IACpB,CAAC;IAED,CAAC,SAAS,CAAC;QACT,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;QAC/D,CAAC;QACD,oBAAoB;QACpB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAA;QAClB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACd,IAAI,CAAC,GAAG,EAAE,CAAA;IACZ,CAAC;IAED,CAAC,OAAO,CAAC;QACP,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE;YAC1C,IAAI,EAAE,EAAE,CAAC;gBACP,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YAC/B,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAA;QAC5B,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,QAAgB;QAC3B,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QAC9C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACd,IAAI,CAAC,GAAG,EAAE,CAAA;IACZ,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,QAAgB;QACzB,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;QAC1D,CAAC;QACD,oBAAoB;QACpB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAA;QAClB,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAClC,CAAA;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACd,IAAI,CAAC,GAAG,EAAE,CAAA;IACZ,CAAC;IAED,CAAC,IAAI,CAAC;QACJ,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;QAC1D,CAAC;QACD,oBAAoB;QACpB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,OAAO,GACX,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAkB,CAAA;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;YAC5C,IAAI,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAA;YACjC,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC5C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACd,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAA;QACnB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAA;IAClB,CAAC;IAED,CAAC,QAAQ,CAAC;QACR,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;YACrC,IAAI,EAAE,EAAE,CAAC;gBACP,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YAC/B,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAA;QACtB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,EAAU;QACrB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACtB,CAAC;QACD,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;QAC1D,CAAC;QACD,qBAAqB;QAErB,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAA;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAA;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QACxD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QACrC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACf,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;QACZ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;QAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;IACd,CAAC;IAED,CAAC,IAAI,CAAC;QACJ,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QAC7C,IAAI,EAAE,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;QAC3D,CAAC;QACD,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE;YACtD,IAAI,EAAE,EAAE,CAAC;gBACP,6DAA6D;gBAC7D,8DAA8D;gBAC9D,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;YAClD,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAA;QACzB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,qBAAqB;IACrB,CAAC,KAAK,CAAC,CACL,KAAyD,GAAG,EAAE,GAAE,CAAC;QAEjE,oBAAoB;QACpB,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS;YAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAClD,CAAC;IAED,CAAC,MAAM,CAAC,CAAC,SAAiB;QACxB,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CACtB,IAAI,KAAK,CAAC,4BAA4B,CAAC,EACvC;gBACE,IAAI,EAAE,IAAI,CAAC,QAAQ;gBACnB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE,KAAK;aACZ,CACF,CAAA;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CACtB,IAAI,KAAK,CAAC,gCAAgC,CAAC,EAC3C;gBACE,IAAI,EAAE,IAAI,CAAC,QAAQ;gBACnB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE,KAAK;aACZ,CACF,CAAA;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;QAChE,CAAC;QACD,oBAAoB;QAEpB,qEAAqE;QACrE,uEAAuE;QACvE,uEAAuE;QACvE,sEAAsE;QACtE,uEAAuE;QACvE,+DAA+D;QAC/D,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9B,KACE,IAAI,CAAC,GAAG,SAAS,EACjB,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,EAC/C,CAAC,EAAE,EACH,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBAC7B,SAAS,EAAE,CAAA;gBACX,IAAI,CAAC,MAAM,EAAE,CAAA;YACf,CAAC;QACH,CAAC;QAED,MAAM,KAAK,GACT,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClD,IAAI,CAAC,GAAG;YACV,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA;QAE3D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACjC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;QACjB,CAAC;IACH,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,EAAa;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IACxB,CAAC;IAQD,KAAK,CACH,KAAsB,EACtB,QAA8C,EAC9C,EAAc;QAEd,sEAAsE;QACtE,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,KAAK,GAAG,MAAM,CAAC,IAAI,CACjB,KAAK,EACL,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CACjD,CAAA;QACH,CAAC;QACD,oBAAoB;QAEpB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CACtB,IAAI,KAAK,CAAC,iCAAiC,CAAC,EAC5C;gBACE,IAAI,EAAE,IAAI,CAAC,QAAQ;aACpB,CACF,CAAA;YACD,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QAC/B,CAAC;QACD,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAA;QAC3B,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAA;QAChC,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,MAAM,CAAA;QACxB,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAA;QAC3B,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;IACrC,CAAC;IAED,CAAC,OAAO,CAAC;QACP,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;YAC7C,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CACtB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CACzC,CAAA;QACH,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;QACnD,CAAC;QACD,oBAAoB;QAEpB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC/B,qEAAqE;YACrE,oDAAoD;YACpD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAC3B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAC5C,CAAA;YACD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACjB,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC3C,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;IACd,CAAC;CACF;AAED,MAAM,OAAO,cAAe,SAAQ,UAAU;IAC5C,IAAI,GAAS,IAAI,CAAC;IAElB,CAAC,KAAK,CAAC;QACL,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC5C,CAAC;IAED,CAAC,OAAO,CAAC;QACP,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;IAClD,CAAC;IAED,CAAC,QAAQ,CAAC;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAA;IACnD,CAAC;IAED,CAAC,IAAI,CAAC;QACJ,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC;YACH,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;YAC7C,qBAAqB;YACrB,IAAI,EAAE,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;YAC1D,CAAC;YACD,oBAAoB;YACpB,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAA;YAC3D,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAA;YACvB,KAAK,GAAG,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACT,6DAA6D;YAC7D,8DAA8D;YAC9D,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC;oBACH,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;gBACvB,CAAC;gBAAC,OAAO,EAAE,EAAE,CAAC,CAAA,CAAC;YACjB,CAAC;QACH,CAAC;IACH,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,EAAa;QACxB,EAAE,EAAE,CAAA;IACN,CAAC;IAED,qBAAqB;IACrB,CAAC,KAAK,CAAC,CACL,KAAyD,GAAG,EAAE,GAAE,CAAC;QAEjE,oBAAoB;QACpB,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS;YAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAChD,EAAE,EAAE,CAAA;IACN,CAAC;CACF;AAED,MAAM,OAAO,aACX,SAAQ,QAA4C;IAGpD,QAAQ,GAAW,CAAC,CAAA;IACpB,WAAW,GAAW,CAAC,CAAA;IACvB,GAAG,GAAW,CAAC,CAAA;IACf,GAAG,GAAW,CAAC,CAAA;IACf,MAAM,GAAW,CAAC,CAAA;IAClB,MAAM,GAAW,CAAC,CAAA;IAClB,aAAa,CAAS;IACtB,QAAQ,CAAS;IACjB,MAAM,CAAS;IACf,KAAK,CAAS;IACd,OAAO,CAAS;IAChB,SAAS,CAAW;IACpB,IAAI,CAAe;IACnB,MAAM,CAAS;IACf,IAAI,CAAQ;IACZ,IAAI,CAAS;IACb,GAAG,CAAS;IACZ,GAAG,CAAS;IACZ,KAAK,CAAS;IACd,KAAK,CAAS;IACd,MAAM,CAAS;IACf,KAAK,CAAO;IACZ,KAAK,CAAO;IACZ,KAAK,CAAO;IACZ,QAAQ,CAAS;IACjB,IAAI,CAAQ;IACZ,YAAY,CAA6B;IAEzC,IAAI,CAAC,IAAY,EAAE,OAAuB,EAAE,OAAiB,EAAE;QAC7D,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED,YACE,SAAoB,EACpB,OAA8B,EAAE;QAEhC,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACzB,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAA;QACxC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAA;QAC9B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAA;QAC5B,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAA;QAEpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,CAAA;QAC1B,qBAAqB;QACrB,IAAI,IAAI,KAAK,aAAa,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;QACzD,CAAC;QACD,oBAAoB;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACrB,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;QAExB,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,IAAI;YACP,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC5B,CAAC,CAAC,SAAS,CAAA;QACb,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAA;QACpD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAA;QACpD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAA;QACxD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAA;QACxD,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;QAC1B,IAAI,CAAC,KAAK;YACR,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAA;QACzD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAA;QACxD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAA;QACxD,IAAI,CAAC,QAAQ;YACX,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;gBAChC,oBAAoB,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAC1C,CAAC,CAAC,SAAS,CAAA;QAEb,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACrC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,QAAQ,GAAmB,KAAK,CAAA;QACpC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACrD,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACzC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;gBACpB,QAAQ,GAAG,IAAI,CAAA;YACjB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAA;QAC5B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,cAAc,CAAA;QAE3C,IAAI,CAAC,YAAY,EAAE,CAAC,IAA6B,CAAC,CAAA;QAClD,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;YACvB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAC7B,QAAQ,EACN,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC7B,CAAC,CAAC,IAAI,CAAC,QAAQ;YACjB,yDAAyD;YACzD,mDAAmD;YACnD,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;YACzC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;YACzC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;YAC5C,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;YAC7C,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;YAC7C,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;SAC9C,CAAC,CAAA;QAEF,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CACP,gBAAgB,EAChB,aAAa,QAAQ,qBAAqB,EAC1C;gBACE,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI;aAC3B,CACF,CAAA;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACxC,KAAK,CAAC,KAAK,CACT,IAAI,GAAG,CAAC;gBACN,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;gBAC7C,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;gBAC7C,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;gBACzC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;gBAC5C,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC7B,QAAQ,EACN,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;oBACnD,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC7B,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;gBACzC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;gBAC7C,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG;gBACnD,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG;gBACnD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK;aACxD,CAAC,CAAC,MAAM,EAAE,CACZ,CAAA;QACH,CAAC;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAA;QAC5B,qBAAqB;QACrB,IAAI,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;QAClD,oBAAoB;QACpB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACd,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC;IAED,CAAC,MAAM,CAAC,CAAC,IAAY;QACnB,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,IAAY;QACjB,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;IAChE,CAAC;IAQD,KAAK,CACH,KAAsB,EACtB,QAA8C,EAC9C,EAAc;QAEd,sEAAsE;QACtE,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,KAAK,GAAG,MAAM,CAAC,IAAI,CACjB,KAAK,EACL,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CACjD,CAAA;QACH,CAAC;QACD,oBAAoB;QACpB,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAA;QAC7B,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;QAC9D,CAAC;QACD,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAA;QAC5B,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;IAC/B,CAAC;IASD,GAAG,CACD,KAAsC,EACtC,QAAwC,EACxC,EAAe;QAEf,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;QAC7C,CAAC;QACD,sEAAsE;QACtE,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAChC,EAAE,GAAG,KAAK,CAAA;YACV,QAAQ,GAAG,SAAS,CAAA;YACpB,KAAK,GAAG,SAAS,CAAA;QACnB,CAAC;QACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,MAAM,CAAC,CAAA;QAChD,CAAC;QACD,IAAI,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC/B,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QAC5C,oBAAoB;QACpB,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAED,MAAM,OAAO,GAAG,CAAC,IAAW,EAAiC,EAAE,CAC7D,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM;IACtB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW;QAClC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,cAAc;YACxC,CAAC,CAAC,aAAa,CAAA", "sourcesContent": ["import fs, { type Stats } from 'fs'\nimport { Minipass } from 'minipass'\nimport path from 'path'\nimport { Header } from './header.js'\nimport { modeFix } from './mode-fix.js'\nimport { normalizeWindowsPath } from './normalize-windows-path.js'\nimport {\n  deal<PERSON>,\n  Link<PERSON><PERSON><PERSON>,\n  TarOptions,\n  TarOptionsWithAliases,\n} from './options.js'\nimport { Pax } from './pax.js'\nimport { ReadEntry } from './read-entry.js'\nimport { stripAbsolutePath } from './strip-absolute-path.js'\nimport { stripTrailingSlashes } from './strip-trailing-slashes.js'\nimport { EntryTypeName } from './types.js'\nimport {\n  WarnData,\n  Warner,\n  WarnEvent,\n  warnMethod,\n} from './warn-method.js'\nimport * as winchars from './winchars.js'\n\nconst prefixPath = (path: string, prefix?: string) => {\n  if (!prefix) {\n    return normalizeWindowsPath(path)\n  }\n  path = normalizeWindowsPath(path).replace(/^\\.(\\/|$)/, '')\n  return stripTrailingSlashes(prefix) + '/' + path\n}\n\nconst maxReadSize = 16 * 1024 * 1024\n\nconst PROCESS = Symbol('process')\nconst FILE = Symbol('file')\nconst DIRECTORY = Symbol('directory')\nconst SYMLINK = Symbol('symlink')\nconst HARDLINK = Symbol('hardlink')\nconst HEADER = Symbol('header')\nconst READ = Symbol('read')\nconst LSTAT = Symbol('lstat')\nconst ONLSTAT = Symbol('onlstat')\nconst ONREAD = Symbol('onread')\nconst ONREADLINK = Symbol('onreadlink')\nconst OPENFILE = Symbol('openfile')\nconst ONOPENFILE = Symbol('onopenfile')\nconst CLOSE = Symbol('close')\nconst MODE = Symbol('mode')\nconst AWAITDRAIN = Symbol('awaitDrain')\nconst ONDRAIN = Symbol('ondrain')\nconst PREFIX = Symbol('prefix')\n\nexport class WriteEntry\n  extends Minipass<Buffer, Minipass.ContiguousData, WarnEvent>\n  implements Warner\n{\n  path: string\n  portable: boolean\n  myuid: number = (process.getuid && process.getuid()) || 0\n  // until node has builtin pwnam functions, this'll have to do\n  myuser: string = process.env.USER || ''\n  maxReadSize: number\n  linkCache: Exclude<TarOptions['linkCache'], undefined>\n  statCache: Exclude<TarOptions['statCache'], undefined>\n  preservePaths: boolean\n  cwd: string\n  strict: boolean\n  mtime?: Date\n  noPax: boolean\n  noMtime: boolean\n  prefix?: string\n  fd?: number\n\n  blockLen: number = 0\n  blockRemain: number = 0\n  buf?: Buffer\n  pos: number = 0\n  remain: number = 0\n  length: number = 0\n  offset: number = 0\n\n  win32: boolean\n  absolute: string\n\n  header?: Header\n  type?: EntryTypeName | 'Unsupported'\n  linkpath?: string\n  stat?: Stats\n  onWriteEntry?: (entry: WriteEntry) => any\n\n  #hadError: boolean = false\n\n  constructor(p: string, opt_: TarOptionsWithAliases = {}) {\n    const opt = dealias(opt_)\n    super()\n    this.path = normalizeWindowsPath(p)\n    // suppress atime, ctime, uid, gid, uname, gname\n    this.portable = !!opt.portable\n    this.maxReadSize = opt.maxReadSize || maxReadSize\n    this.linkCache = opt.linkCache || new Map()\n    this.statCache = opt.statCache || new Map()\n    this.preservePaths = !!opt.preservePaths\n    this.cwd = normalizeWindowsPath(opt.cwd || process.cwd())\n    this.strict = !!opt.strict\n    this.noPax = !!opt.noPax\n    this.noMtime = !!opt.noMtime\n    this.mtime = opt.mtime\n    this.prefix =\n      opt.prefix ? normalizeWindowsPath(opt.prefix) : undefined\n    this.onWriteEntry = opt.onWriteEntry\n\n    if (typeof opt.onwarn === 'function') {\n      this.on('warn', opt.onwarn)\n    }\n\n    let pathWarn: string | boolean = false\n    if (!this.preservePaths) {\n      const [root, stripped] = stripAbsolutePath(this.path)\n      if (root && typeof stripped === 'string') {\n        this.path = stripped\n        pathWarn = root\n      }\n    }\n\n    this.win32 = !!opt.win32 || process.platform === 'win32'\n    if (this.win32) {\n      // force the \\ to / normalization, since we might not *actually*\n      // be on windows, but want \\ to be considered a path separator.\n      this.path = winchars.decode(this.path.replace(/\\\\/g, '/'))\n      p = p.replace(/\\\\/g, '/')\n    }\n\n    this.absolute = normalizeWindowsPath(\n      opt.absolute || path.resolve(this.cwd, p),\n    )\n\n    if (this.path === '') {\n      this.path = './'\n    }\n\n    if (pathWarn) {\n      this.warn(\n        'TAR_ENTRY_INFO',\n        `stripping ${pathWarn} from absolute path`,\n        {\n          entry: this,\n          path: pathWarn + this.path,\n        },\n      )\n    }\n\n    const cs = this.statCache.get(this.absolute)\n    if (cs) {\n      this[ONLSTAT](cs)\n    } else {\n      this[LSTAT]()\n    }\n  }\n\n  warn(code: string, message: string | Error, data: WarnData = {}) {\n    return warnMethod(this, code, message, data)\n  }\n\n  emit(ev: keyof WarnEvent, ...data: any[]) {\n    if (ev === 'error') {\n      this.#hadError = true\n    }\n    return super.emit(ev, ...data)\n  }\n\n  [LSTAT]() {\n    fs.lstat(this.absolute, (er, stat) => {\n      if (er) {\n        return this.emit('error', er)\n      }\n      this[ONLSTAT](stat)\n    })\n  }\n\n  [ONLSTAT](stat: Stats) {\n    this.statCache.set(this.absolute, stat)\n    this.stat = stat\n    if (!stat.isFile()) {\n      stat.size = 0\n    }\n    this.type = getType(stat)\n    this.emit('stat', stat)\n    this[PROCESS]()\n  }\n\n  [PROCESS]() {\n    switch (this.type) {\n      case 'File':\n        return this[FILE]()\n      case 'Directory':\n        return this[DIRECTORY]()\n      case 'SymbolicLink':\n        return this[SYMLINK]()\n      // unsupported types are ignored.\n      default:\n        return this.end()\n    }\n  }\n\n  [MODE](mode: number) {\n    return modeFix(mode, this.type === 'Directory', this.portable)\n  }\n\n  [PREFIX](path: string) {\n    return prefixPath(path, this.prefix)\n  }\n\n  [HEADER]() {\n    /* c8 ignore start */\n    if (!this.stat) {\n      throw new Error('cannot write header before stat')\n    }\n    /* c8 ignore stop */\n\n    if (this.type === 'Directory' && this.portable) {\n      this.noMtime = true\n    }\n\n    this.onWriteEntry?.(this)\n    this.header = new Header({\n      path: this[PREFIX](this.path),\n      // only apply the prefix to hard links.\n      linkpath:\n        this.type === 'Link' && this.linkpath !== undefined ?\n          this[PREFIX](this.linkpath)\n        : this.linkpath,\n      // only the permissions and setuid/setgid/sticky bitflags\n      // not the higher-order bits that specify file type\n      mode: this[MODE](this.stat.mode),\n      uid: this.portable ? undefined : this.stat.uid,\n      gid: this.portable ? undefined : this.stat.gid,\n      size: this.stat.size,\n      mtime: this.noMtime ? undefined : this.mtime || this.stat.mtime,\n      /* c8 ignore next */\n      type: this.type === 'Unsupported' ? undefined : this.type,\n      uname:\n        this.portable ? undefined\n        : this.stat.uid === this.myuid ? this.myuser\n        : '',\n      atime: this.portable ? undefined : this.stat.atime,\n      ctime: this.portable ? undefined : this.stat.ctime,\n    })\n\n    if (this.header.encode() && !this.noPax) {\n      super.write(\n        new Pax({\n          atime: this.portable ? undefined : this.header.atime,\n          ctime: this.portable ? undefined : this.header.ctime,\n          gid: this.portable ? undefined : this.header.gid,\n          mtime:\n            this.noMtime ? undefined : (\n              this.mtime || this.header.mtime\n            ),\n          path: this[PREFIX](this.path),\n          linkpath:\n            this.type === 'Link' && this.linkpath !== undefined ?\n              this[PREFIX](this.linkpath)\n            : this.linkpath,\n          size: this.header.size,\n          uid: this.portable ? undefined : this.header.uid,\n          uname: this.portable ? undefined : this.header.uname,\n          dev: this.portable ? undefined : this.stat.dev,\n          ino: this.portable ? undefined : this.stat.ino,\n          nlink: this.portable ? undefined : this.stat.nlink,\n        }).encode(),\n      )\n    }\n    const block = this.header?.block\n    /* c8 ignore start */\n    if (!block) {\n      throw new Error('failed to encode header')\n    }\n    /* c8 ignore stop */\n    super.write(block)\n  }\n\n  [DIRECTORY]() {\n    /* c8 ignore start */\n    if (!this.stat) {\n      throw new Error('cannot create directory entry without stat')\n    }\n    /* c8 ignore stop */\n    if (this.path.slice(-1) !== '/') {\n      this.path += '/'\n    }\n    this.stat.size = 0\n    this[HEADER]()\n    this.end()\n  }\n\n  [SYMLINK]() {\n    fs.readlink(this.absolute, (er, linkpath) => {\n      if (er) {\n        return this.emit('error', er)\n      }\n      this[ONREADLINK](linkpath)\n    })\n  }\n\n  [ONREADLINK](linkpath: string) {\n    this.linkpath = normalizeWindowsPath(linkpath)\n    this[HEADER]()\n    this.end()\n  }\n\n  [HARDLINK](linkpath: string) {\n    /* c8 ignore start */\n    if (!this.stat) {\n      throw new Error('cannot create link entry without stat')\n    }\n    /* c8 ignore stop */\n    this.type = 'Link'\n    this.linkpath = normalizeWindowsPath(\n      path.relative(this.cwd, linkpath),\n    )\n    this.stat.size = 0\n    this[HEADER]()\n    this.end()\n  }\n\n  [FILE]() {\n    /* c8 ignore start */\n    if (!this.stat) {\n      throw new Error('cannot create file entry without stat')\n    }\n    /* c8 ignore stop */\n    if (this.stat.nlink > 1) {\n      const linkKey =\n        `${this.stat.dev}:${this.stat.ino}` as LinkCacheKey\n      const linkpath = this.linkCache.get(linkKey)\n      if (linkpath?.indexOf(this.cwd) === 0) {\n        return this[HARDLINK](linkpath)\n      }\n      this.linkCache.set(linkKey, this.absolute)\n    }\n\n    this[HEADER]()\n    if (this.stat.size === 0) {\n      return this.end()\n    }\n\n    this[OPENFILE]()\n  }\n\n  [OPENFILE]() {\n    fs.open(this.absolute, 'r', (er, fd) => {\n      if (er) {\n        return this.emit('error', er)\n      }\n      this[ONOPENFILE](fd)\n    })\n  }\n\n  [ONOPENFILE](fd: number) {\n    this.fd = fd\n    if (this.#hadError) {\n      return this[CLOSE]()\n    }\n    /* c8 ignore start */\n    if (!this.stat) {\n      throw new Error('should stat before calling onopenfile')\n    }\n    /* c8 ignore start */\n\n    this.blockLen = 512 * Math.ceil(this.stat.size / 512)\n    this.blockRemain = this.blockLen\n    const bufLen = Math.min(this.blockLen, this.maxReadSize)\n    this.buf = Buffer.allocUnsafe(bufLen)\n    this.offset = 0\n    this.pos = 0\n    this.remain = this.stat.size\n    this.length = this.buf.length\n    this[READ]()\n  }\n\n  [READ]() {\n    const { fd, buf, offset, length, pos } = this\n    if (fd === undefined || buf === undefined) {\n      throw new Error('cannot read file without first opening')\n    }\n    fs.read(fd, buf, offset, length, pos, (er, bytesRead) => {\n      if (er) {\n        // ignoring the error from close(2) is a bad practice, but at\n        // this point we already have an error, don't need another one\n        return this[CLOSE](() => this.emit('error', er))\n      }\n      this[ONREAD](bytesRead)\n    })\n  }\n\n  /* c8 ignore start */\n  [CLOSE](\n    cb: (er?: null | Error | NodeJS.ErrnoException) => any = () => {},\n  ) {\n    /* c8 ignore stop */\n    if (this.fd !== undefined) fs.close(this.fd, cb)\n  }\n\n  [ONREAD](bytesRead: number) {\n    if (bytesRead <= 0 && this.remain > 0) {\n      const er = Object.assign(\n        new Error('encountered unexpected EOF'),\n        {\n          path: this.absolute,\n          syscall: 'read',\n          code: 'EOF',\n        },\n      )\n      return this[CLOSE](() => this.emit('error', er))\n    }\n\n    if (bytesRead > this.remain) {\n      const er = Object.assign(\n        new Error('did not encounter expected EOF'),\n        {\n          path: this.absolute,\n          syscall: 'read',\n          code: 'EOF',\n        },\n      )\n      return this[CLOSE](() => this.emit('error', er))\n    }\n\n    /* c8 ignore start */\n    if (!this.buf) {\n      throw new Error('should have created buffer prior to reading')\n    }\n    /* c8 ignore stop */\n\n    // null out the rest of the buffer, if we could fit the block padding\n    // at the end of this loop, we've incremented bytesRead and this.remain\n    // to be incremented up to the blockRemain level, as if we had expected\n    // to get a null-padded file, and read it until the end.  then we will\n    // decrement both remain and blockRemain by bytesRead, and know that we\n    // reached the expected EOF, without any null buffer to append.\n    if (bytesRead === this.remain) {\n      for (\n        let i = bytesRead;\n        i < this.length && bytesRead < this.blockRemain;\n        i++\n      ) {\n        this.buf[i + this.offset] = 0\n        bytesRead++\n        this.remain++\n      }\n    }\n\n    const chunk =\n      this.offset === 0 && bytesRead === this.buf.length ?\n        this.buf\n      : this.buf.subarray(this.offset, this.offset + bytesRead)\n\n    const flushed = this.write(chunk)\n    if (!flushed) {\n      this[AWAITDRAIN](() => this[ONDRAIN]())\n    } else {\n      this[ONDRAIN]()\n    }\n  }\n\n  [AWAITDRAIN](cb: () => any) {\n    this.once('drain', cb)\n  }\n\n  write(buffer: Buffer | string, cb?: () => void): boolean\n  write(\n    str: Buffer | string,\n    encoding?: BufferEncoding | null,\n    cb?: () => void,\n  ): boolean\n  write(\n    chunk: Buffer | string,\n    encoding?: BufferEncoding | (() => any) | null,\n    cb?: () => any,\n  ): boolean {\n    /* c8 ignore start - just junk to comply with NodeJS.WritableStream */\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n    if (typeof chunk === 'string') {\n      chunk = Buffer.from(\n        chunk,\n        typeof encoding === 'string' ? encoding : 'utf8',\n      )\n    }\n    /* c8 ignore stop */\n\n    if (this.blockRemain < chunk.length) {\n      const er = Object.assign(\n        new Error('writing more data than expected'),\n        {\n          path: this.absolute,\n        },\n      )\n      return this.emit('error', er)\n    }\n    this.remain -= chunk.length\n    this.blockRemain -= chunk.length\n    this.pos += chunk.length\n    this.offset += chunk.length\n    return super.write(chunk, null, cb)\n  }\n\n  [ONDRAIN]() {\n    if (!this.remain) {\n      if (this.blockRemain) {\n        super.write(Buffer.alloc(this.blockRemain))\n      }\n      return this[CLOSE](er =>\n        er ? this.emit('error', er) : this.end(),\n      )\n    }\n\n    /* c8 ignore start */\n    if (!this.buf) {\n      throw new Error('buffer lost somehow in ONDRAIN')\n    }\n    /* c8 ignore stop */\n\n    if (this.offset >= this.length) {\n      // if we only have a smaller bit left to read, alloc a smaller buffer\n      // otherwise, keep it the same length it was before.\n      this.buf = Buffer.allocUnsafe(\n        Math.min(this.blockRemain, this.buf.length),\n      )\n      this.offset = 0\n    }\n    this.length = this.buf.length - this.offset\n    this[READ]()\n  }\n}\n\nexport class WriteEntrySync extends WriteEntry implements Warner {\n  sync: true = true;\n\n  [LSTAT]() {\n    this[ONLSTAT](fs.lstatSync(this.absolute))\n  }\n\n  [SYMLINK]() {\n    this[ONREADLINK](fs.readlinkSync(this.absolute))\n  }\n\n  [OPENFILE]() {\n    this[ONOPENFILE](fs.openSync(this.absolute, 'r'))\n  }\n\n  [READ]() {\n    let threw = true\n    try {\n      const { fd, buf, offset, length, pos } = this\n      /* c8 ignore start */\n      if (fd === undefined || buf === undefined) {\n        throw new Error('fd and buf must be set in READ method')\n      }\n      /* c8 ignore stop */\n      const bytesRead = fs.readSync(fd, buf, offset, length, pos)\n      this[ONREAD](bytesRead)\n      threw = false\n    } finally {\n      // ignoring the error from close(2) is a bad practice, but at\n      // this point we already have an error, don't need another one\n      if (threw) {\n        try {\n          this[CLOSE](() => {})\n        } catch (er) {}\n      }\n    }\n  }\n\n  [AWAITDRAIN](cb: () => any) {\n    cb()\n  }\n\n  /* c8 ignore start */\n  [CLOSE](\n    cb: (er?: null | Error | NodeJS.ErrnoException) => any = () => {},\n  ) {\n    /* c8 ignore stop */\n    if (this.fd !== undefined) fs.closeSync(this.fd)\n    cb()\n  }\n}\n\nexport class WriteEntryTar\n  extends Minipass<Buffer, Buffer | string, WarnEvent>\n  implements Warner\n{\n  blockLen: number = 0\n  blockRemain: number = 0\n  buf: number = 0\n  pos: number = 0\n  remain: number = 0\n  length: number = 0\n  preservePaths: boolean\n  portable: boolean\n  strict: boolean\n  noPax: boolean\n  noMtime: boolean\n  readEntry: ReadEntry\n  type: EntryTypeName\n  prefix?: string\n  path: string\n  mode?: number\n  uid?: number\n  gid?: number\n  uname?: string\n  gname?: string\n  header?: Header\n  mtime?: Date\n  atime?: Date\n  ctime?: Date\n  linkpath?: string\n  size: number\n  onWriteEntry?: (entry: WriteEntry) => any\n\n  warn(code: string, message: string | Error, data: WarnData = {}) {\n    return warnMethod(this, code, message, data)\n  }\n\n  constructor(\n    readEntry: ReadEntry,\n    opt_: TarOptionsWithAliases = {},\n  ) {\n    const opt = dealias(opt_)\n    super()\n    this.preservePaths = !!opt.preservePaths\n    this.portable = !!opt.portable\n    this.strict = !!opt.strict\n    this.noPax = !!opt.noPax\n    this.noMtime = !!opt.noMtime\n    this.onWriteEntry = opt.onWriteEntry\n\n    this.readEntry = readEntry\n    const { type } = readEntry\n    /* c8 ignore start */\n    if (type === 'Unsupported') {\n      throw new Error('writing entry that should be ignored')\n    }\n    /* c8 ignore stop */\n    this.type = type\n    if (this.type === 'Directory' && this.portable) {\n      this.noMtime = true\n    }\n\n    this.prefix = opt.prefix\n\n    this.path = normalizeWindowsPath(readEntry.path)\n    this.mode =\n      readEntry.mode !== undefined ?\n        this[MODE](readEntry.mode)\n      : undefined\n    this.uid = this.portable ? undefined : readEntry.uid\n    this.gid = this.portable ? undefined : readEntry.gid\n    this.uname = this.portable ? undefined : readEntry.uname\n    this.gname = this.portable ? undefined : readEntry.gname\n    this.size = readEntry.size\n    this.mtime =\n      this.noMtime ? undefined : opt.mtime || readEntry.mtime\n    this.atime = this.portable ? undefined : readEntry.atime\n    this.ctime = this.portable ? undefined : readEntry.ctime\n    this.linkpath =\n      readEntry.linkpath !== undefined ?\n        normalizeWindowsPath(readEntry.linkpath)\n      : undefined\n\n    if (typeof opt.onwarn === 'function') {\n      this.on('warn', opt.onwarn)\n    }\n\n    let pathWarn: false | string = false\n    if (!this.preservePaths) {\n      const [root, stripped] = stripAbsolutePath(this.path)\n      if (root && typeof stripped === 'string') {\n        this.path = stripped\n        pathWarn = root\n      }\n    }\n\n    this.remain = readEntry.size\n    this.blockRemain = readEntry.startBlockSize\n\n    this.onWriteEntry?.(this as unknown as WriteEntry)\n    this.header = new Header({\n      path: this[PREFIX](this.path),\n      linkpath:\n        this.type === 'Link' && this.linkpath !== undefined ?\n          this[PREFIX](this.linkpath)\n        : this.linkpath,\n      // only the permissions and setuid/setgid/sticky bitflags\n      // not the higher-order bits that specify file type\n      mode: this.mode,\n      uid: this.portable ? undefined : this.uid,\n      gid: this.portable ? undefined : this.gid,\n      size: this.size,\n      mtime: this.noMtime ? undefined : this.mtime,\n      type: this.type,\n      uname: this.portable ? undefined : this.uname,\n      atime: this.portable ? undefined : this.atime,\n      ctime: this.portable ? undefined : this.ctime,\n    })\n\n    if (pathWarn) {\n      this.warn(\n        'TAR_ENTRY_INFO',\n        `stripping ${pathWarn} from absolute path`,\n        {\n          entry: this,\n          path: pathWarn + this.path,\n        },\n      )\n    }\n\n    if (this.header.encode() && !this.noPax) {\n      super.write(\n        new Pax({\n          atime: this.portable ? undefined : this.atime,\n          ctime: this.portable ? undefined : this.ctime,\n          gid: this.portable ? undefined : this.gid,\n          mtime: this.noMtime ? undefined : this.mtime,\n          path: this[PREFIX](this.path),\n          linkpath:\n            this.type === 'Link' && this.linkpath !== undefined ?\n              this[PREFIX](this.linkpath)\n            : this.linkpath,\n          size: this.size,\n          uid: this.portable ? undefined : this.uid,\n          uname: this.portable ? undefined : this.uname,\n          dev: this.portable ? undefined : this.readEntry.dev,\n          ino: this.portable ? undefined : this.readEntry.ino,\n          nlink: this.portable ? undefined : this.readEntry.nlink,\n        }).encode(),\n      )\n    }\n\n    const b = this.header?.block\n    /* c8 ignore start */\n    if (!b) throw new Error('failed to encode header')\n    /* c8 ignore stop */\n    super.write(b)\n    readEntry.pipe(this)\n  }\n\n  [PREFIX](path: string) {\n    return prefixPath(path, this.prefix)\n  }\n\n  [MODE](mode: number) {\n    return modeFix(mode, this.type === 'Directory', this.portable)\n  }\n\n  write(buffer: Buffer | string, cb?: () => void): boolean\n  write(\n    str: Buffer | string,\n    encoding?: BufferEncoding | null,\n    cb?: () => void,\n  ): boolean\n  write(\n    chunk: Buffer | string,\n    encoding?: BufferEncoding | (() => any) | null,\n    cb?: () => any,\n  ): boolean {\n    /* c8 ignore start - just junk to comply with NodeJS.WritableStream */\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n    if (typeof chunk === 'string') {\n      chunk = Buffer.from(\n        chunk,\n        typeof encoding === 'string' ? encoding : 'utf8',\n      )\n    }\n    /* c8 ignore stop */\n    const writeLen = chunk.length\n    if (writeLen > this.blockRemain) {\n      throw new Error('writing more to entry than is appropriate')\n    }\n    this.blockRemain -= writeLen\n    return super.write(chunk, cb)\n  }\n\n  end(cb?: () => void): this\n  end(chunk: Buffer | string, cb?: () => void): this\n  end(\n    chunk: Buffer | string,\n    encoding?: BufferEncoding,\n    cb?: () => void,\n  ): this\n  end(\n    chunk?: Buffer | string | (() => void),\n    encoding?: BufferEncoding | (() => void),\n    cb?: () => void,\n  ): this {\n    if (this.blockRemain) {\n      super.write(Buffer.alloc(this.blockRemain))\n    }\n    /* c8 ignore start - just junk to comply with NodeJS.WritableStream */\n    if (typeof chunk === 'function') {\n      cb = chunk\n      encoding = undefined\n      chunk = undefined\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n    if (typeof chunk === 'string') {\n      chunk = Buffer.from(chunk, encoding ?? 'utf8')\n    }\n    if (cb) this.once('finish', cb)\n    chunk ? super.end(chunk, cb) : super.end(cb)\n    /* c8 ignore stop */\n    return this\n  }\n}\n\nconst getType = (stat: Stats): EntryTypeName | 'Unsupported' =>\n  stat.isFile() ? 'File'\n  : stat.isDirectory() ? 'Directory'\n  : stat.isSymbolicLink() ? 'SymbolicLink'\n  : 'Unsupported'\n"]}