/* Base styles */

:root {
  font-family: 'Segoe UI', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* OS-like styles */
.window {
  background-color: white;
  border: 1px solid #d1d1d1;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
}

.window.active {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
}

.window-header {
  background-color: #f3f2f1;
  border-bottom: 1px solid #d1d1d1;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: move;
}

.window-header.dark {
  background-color: #2d2d30;
  color: white;
}

.window-controls {
  display: flex;
  gap: 4px;
}

.window-control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.window-control.close {
  background-color: #ef4444;
}

.window-control.close:hover {
  background-color: #dc2626;
}

.window-control.minimize {
  background-color: #eab308;
}

.window-control.minimize:hover {
  background-color: #ca8a04;
}

.window-control.maximize {
  background-color: #22c55e;
}

.window-control.maximize:hover {
  background-color: #16a34a;
}

.taskbar {
  background-color: #f3f2f1;
  border-top: 1px solid #d1d1d1;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.taskbar.dark {
  background-color: #2d2d30;
  border-color: #4b5563;
}

.desktop-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  color: white;
  transition: background-color 0.2s;
}

.desktop-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.desktop-icon span {
  font-size: 12px;
  margin-top: 4px;
  text-align: center;
}

.desktop-icons-container {
  position: absolute;
  top: 16px;
  left: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

/* React Resizable Styles */
.react-resizable {
  position: relative;
}

.react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGZpbGw9IiM0QTVDNkEiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PHBhdGggZD0ibTUgNWgtNHYtNGg0eiIvPjwvZz48L3N2Zz4=');
  background-position: bottom right;
  padding: 0 3px 3px 0;
}

.react-resizable-handle-se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.react-resizable-handle-s {
  bottom: 0;
  left: 50%;
  margin-left: -10px;
  cursor: s-resize;
}

.react-resizable-handle-e {
  right: 0;
  top: 50%;
  margin-top: -10px;
  cursor: e-resize;
}

.react-resizable-handle-n {
  top: 0;
  left: 50%;
  margin-left: -10px;
  cursor: n-resize;
}

.react-resizable-handle-w {
  left: 0;
  top: 50%;
  margin-top: -10px;
  cursor: w-resize;
}

.react-resizable-handle-ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
}

.react-resizable-handle-nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
}

.react-resizable-handle-sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .desktop-icon {
    padding: 4px;
  }

  .desktop-icon span {
    font-size: 10px;
  }

  .taskbar {
    padding: 2px 4px;
  }

  .window-header {
    padding: 4px 8px;
  }

  .window-control {
    width: 16px;
    height: 16px;
  }

  /* Hide resize handles on mobile */
  .react-resizable-handle {
    display: none;
  }
}
