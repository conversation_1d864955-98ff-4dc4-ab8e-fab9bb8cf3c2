import React, { useState, useCallback } from 'react';
import Window from './Window';
import Taskbar from './Taskbar';
import DesktopIcon from './DesktopIcon';
import { FileText, Folder, Settings, Terminal, Globe, Calculator } from 'lucide-react';

const Desktop = () => {
  const [windows, setWindows] = useState([]);
  const [activeWindowId, setActiveWindowId] = useState(null);
  const [nextWindowId, setNextWindowId] = useState(1);

  const desktopIcons = [
    { id: 'blog-post', name: 'Why OS Blog Post', icon: FileText, type: 'document' },
    { id: 'file-explorer', name: 'File Explorer', icon: Folder, type: 'explorer' },
    { id: 'settings', name: 'Settings', icon: Settings, type: 'settings' },
    { id: 'terminal', name: 'Terminal', icon: Terminal, type: 'terminal' },
    { id: 'browser', name: '<PERSON><PERSON><PERSON>', icon: Globe, type: 'browser' },
    { id: 'calculator', name: 'Calculator', icon: Calculator, type: 'calculator' },
  ];

  const openWindow = useCallback((iconData) => {
    const windowId = nextWindowId;

    // Responsive window sizing
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const isMobile = screenWidth < 768;

    const newWindow = {
      id: windowId,
      title: iconData.name,
      type: iconData.type,
      x: isMobile ? 10 : 100 + (windows.length * 30),
      y: isMobile ? 10 : 100 + (windows.length * 30),
      width: isMobile ? screenWidth - 20 : Math.min(800, screenWidth - 200),
      height: isMobile ? screenHeight - 100 : Math.min(600, screenHeight - 200),
      minimized: false,
      maximized: isMobile,
    };

    setWindows(prev => [...prev, newWindow]);
    setActiveWindowId(windowId);
    setNextWindowId(prev => prev + 1);
  }, [windows.length, nextWindowId]);

  const closeWindow = useCallback((windowId) => {
    setWindows(prev => prev.filter(w => w.id !== windowId));
    if (activeWindowId === windowId) {
      const remainingWindows = windows.filter(w => w.id !== windowId);
      setActiveWindowId(remainingWindows.length > 0 ? remainingWindows[remainingWindows.length - 1].id : null);
    }
  }, [activeWindowId, windows]);

  const minimizeWindow = useCallback((windowId) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, minimized: true } : w
    ));
  }, []);

  const maximizeWindow = useCallback((windowId) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, maximized: !w.maximized } : w
    ));
  }, []);

  const updateWindowPosition = useCallback((windowId, x, y) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, x, y } : w
    ));
  }, []);

  const updateWindowSize = useCallback((windowId, width, height) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, width, height } : w
    ));
  }, []);

  const bringToFront = useCallback((windowId) => {
    setActiveWindowId(windowId);
    setWindows(prev => {
      const window = prev.find(w => w.id === windowId);
      const otherWindows = prev.filter(w => w.id !== windowId);
      return [...otherWindows, window];
    });
  }, []);

  const restoreWindow = useCallback((windowId) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, minimized: false } : w
    ));
    bringToFront(windowId);
  }, [bringToFront]);

  return (
    <div className="h-screen w-screen relative overflow-hidden">
      {/* Desktop Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500">
        {/* Desktop Icons */}
        <div className="desktop-icons-container">
          {desktopIcons.map((icon) => (
            <DesktopIcon
              key={icon.id}
              icon={icon.icon}
              name={icon.name}
              onClick={() => openWindow(icon)}
            />
          ))}
        </div>

        {/* Windows */}
        {windows.map((window) => (
          <Window
            key={window.id}
            {...window}
            isActive={activeWindowId === window.id}
            onClose={() => closeWindow(window.id)}
            onMinimize={() => minimizeWindow(window.id)}
            onMaximize={() => maximizeWindow(window.id)}
            onPositionChange={(x, y) => updateWindowPosition(window.id, x, y)}
            onSizeChange={(width, height) => updateWindowSize(window.id, width, height)}
            onFocus={() => bringToFront(window.id)}
          />
        ))}
      </div>

      {/* Taskbar */}
      <Taskbar
        windows={windows}
        activeWindowId={activeWindowId}
        onWindowClick={restoreWindow}
      />
    </div>
  );
};

export default Desktop;
